package com.hongjing.oee_mes.repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hongjing.oee_mes.domain.Device;
import com.hongjing.oee_mes.domain.Line;
import com.hongjing.oee_mes.domain.Mo;
import com.hongjing.oee_mes.domain.StandardCapacity;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

@Repository
@SuppressWarnings("unchecked")
public class OeeRepository {

	@PersistenceContext
	private EntityManager entityManager;

	public List<Line> getLineList() {
		String sql = "select pl_id code,pl_name name from TB_BS_PL where disable = 'N' order by pl_id ";
		Query query = entityManager.createNativeQuery(sql);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Line((String) row[0], (String) row[1])).toList();
	}

	public List<Device> getDeviceListByLine(String code) {
		String sql = "";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("code", code);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream().map(row -> new Device((String) row[0], (String) row[1])).toList();
	}

	/**
	 * 获取线体和产品所对应产能
	 * @param prodId 产品ID
	 * @param plId 线体ID
	 */
	public List<StandardCapacity> getStandardCapacity(String prodId, String plId, String bomSide) {
		String sql = "SELECT STD_CT, STD_CY\r\n" + //
				"FROM TB_BS_CY\r\n" + //
				"WHERE PROD_ID = :prodId\r\n" + //
				"  AND PL_ID = :plId\r\n" + //
				"  AND BOM_SIDE = :bomSide";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("prodId", prodId);
		query.setParameter("plId", plId);
		query.setParameter("bomSide", bomSide);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new StandardCapacity(((BigDecimal) row[0]).longValue(), ((BigDecimal) row[1]).longValue()))
			.toList();
	}

	/**
	 * 根据线体ID和日期查询MO信息
	 * @param plId 线体ID
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return MO列表
	 */
	public List<Mo> getMoByPlIdAndDate(String plId, Date startDate, Date endDate) {
		String sql = "SELECT MO, PL_ID, T_BDATE " +
				"FROM TB_PP_MO " +
				"WHERE PL_ID = :plId " +
				"AND T_BDATE >= :startDate " +
				"AND T_BDATE < :endDate";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("plId", plId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);
		List<Object[]> resultList = query.getResultList();
		return resultList.stream()
			.map(row -> new Mo((String) row[0], (String) row[1], (Date) row[2]))
			.toList();
	}

}
