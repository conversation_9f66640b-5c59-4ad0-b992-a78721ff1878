# MO查询接口文档

## 接口描述
根据线体ID和日期范围查询TB_PP_MO表中的MO信息。

## 接口地址
```
GET /oee/mo
```

## 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| plId | String | 是 | 线体ID | SMT1-1 |
| startDate | String | 是 | 开始日期时间 | 2025-07-08 08:00:00 |
| endDate | String | 是 | 结束日期时间 | 2025-07-09 08:00:00 |

## 请求示例
```
GET /oee/mo?plId=SMT1-1&startDate=2025-07-08 08:00:00&endDate=2025-07-09 08:00:00
```

## 响应格式
```json
[
  {
    "mo": "MO001",
    "plId": "SMT1-1",
    "tbdate": "2025-07-08T08:30:00.000+00:00"
  },
  {
    "mo": "MO002", 
    "plId": "SMT1-1",
    "tbdate": "2025-07-08T09:15:00.000+00:00"
  }
]
```

## 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| mo | String | 制造订单号 |
| plId | String | 线体ID |
| tbdate | Date | 订单日期时间 |

## SQL查询语句
原始查询需求：
```sql
select MO,PL_ID,T_BDATE from TB_PP_MO 
where PL_ID = 'SMT1-1' 
and T_BDATE >= TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS')
AND T_BDATE < TO_DATE('2025-07-08 08:00:00', 'YYYY-MM-DD HH24:MI:SS') + 1;
```

注意：原始查询中的日期条件有问题（开始和结束时间相同），接口实现中已修正为使用不同的开始和结束时间。

## 实现说明
1. **实体类**: `Mo.java` - 对应TB_PP_MO表的查询结果
2. **Repository**: `OeeRepository.getMoByPlIdAndDate()` - 执行数据库查询
3. **Service**: `OeeService.getMoByPlIdAndDate()` - 业务逻辑层
4. **Controller**: `OeeController.getMoByPlIdAndDate()` - REST接口层

## 错误处理
- 日期格式错误会返回400 Bad Request
- 数据库连接错误会返回500 Internal Server Error
- 参数缺失会返回400 Bad Request

## 测试
可以使用以下curl命令测试接口：
```bash
curl -X GET "http://localhost:9996/oee-mes-server/oee/mo?plId=SMT1-1&startDate=2025-07-08%2008:00:00&endDate=2025-07-09%2008:00:00"
```
